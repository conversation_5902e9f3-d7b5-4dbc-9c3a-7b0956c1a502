package com.ygxj.apa.data.entity

import java.io.Serializable

data class RoomEntityV2(
    val sort: Int = 0, // 1
    val title: String = "", // 体感互动
    val content: String = "", // 体感互动放松系统软件安装简便，易操作，由管理中心、训练中心、减压中心、数据中心等模块组成，帮助用户了解自身情绪控制效果与身心调节状态，
    val contentTxt: String = "", // 体感互动放松系统软件安装简便，易操作，由管理中心、训练中心、减压中心、数据中心等模块组成，帮助用户了解自身情绪控制效果与身心调节状态，锻炼认知能力、生理机能协调性。管理员可以对用户信息进行统一的编辑、管理、筛查、权
    val url: String = "", // /base/images/getFileFullPath/data/attachment/aiPsyStatic/platform/ai_psy_helper/2/2420440438800385体感互动.png
    val audioUrl: String = "", // /base/images/getAudios/base/images/getAudios/data/attachment/aiPsyStatic/platform/ai_psy_helper/2/24204405918924812418441327411201feng_lin_da_dao.mp4
    val createTime: String = "", // 2025-05-09
    // yaw,x,y,z
    val coordinate: String = "", // -177.34,10.12,-0.98,0,
    val keyword: String = "" 
) : Serializable {

    fun getPosition(): RobotPosEntity? {
        val split = coordinate.split(",")
        if (split.size != 4) return null
        return RobotPosEntity(
            x = split[1].toFloat(),
            y = split[2].toFloat(),
            z = split[3].toFloat(),
            rotation = split[0].toFloat()
        )
    }
}